#!/usr/bin/env python3
"""
测试消息处理功能
"""
import os
import sys
import asyncio
import json
import time
from loguru import logger

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 加载 .env 文件
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

try:
    import aio_pika
    PIKA_AVAILABLE = True
except ImportError:
    PIKA_AVAILABLE = False
    logger.error("aio_pika not available")


async def send_test_messages():
    """发送各种类型的测试消息"""
    if not PIKA_AVAILABLE:
        logger.error("aio_pika不可用")
        return False
    
    try:
        # 连接MQ
        mq_url = os.getenv('MQ_URL', '**********************************/')
        conn = await aio_pika.connect_robust(mq_url)
        ch = await conn.channel()
        
        # 测试消息列表 - 使用YBA组的wxid
        test_wxid = "wxid_yba19990312"  # YBA主账号的wxid
        test_messages = [
            {
                "msg_id": 1001,
                "msg_type": 1,  # 文本消息
                "content": "这是一条测试文本消息，包含帮助关键词",
                "from_user_name": "张三",
                "to_user_name": test_wxid,
                "timestamp": int(time.time()),
                "is_self_message": False,
                "wxid": test_wxid,
                "uuid": "test_uuid_001",
                "push_content": "",
                "msg_source": "",
                "ver": 3,
                "msg_name": "AddMsgs"
            },
            {
                "msg_id": 1002,
                "msg_type": 1,  # 文本消息（群聊）
                "content": "这是群聊中的消息",
                "from_user_name": "李四",
                "to_user_name": "test_group@chatroom",
                "timestamp": int(time.time()),
                "is_self_message": False,
                "wxid": test_wxid,
                "uuid": "test_uuid_002",
                "push_content": "",
                "msg_source": "",
                "ver": 3,
                "msg_name": "AddMsgs",
                "group_id": "test_group@chatroom"
            },
            {
                "msg_id": 1003,
                "msg_type": 3,  # 图片消息
                "content": "[图片]",
                "from_user_name": "王五",
                "to_user_name": test_wxid,
                "timestamp": int(time.time()),
                "is_self_message": False,
                "wxid": test_wxid,
                "uuid": "test_uuid_003",
                "push_content": "",
                "msg_source": "",
                "ver": 3,
                "msg_name": "AddMsgs"
            },
            {
                "msg_id": 1004,
                "msg_type": 47,  # 表情消息
                "content": "[表情]",
                "from_user_name": "赵六",
                "to_user_name": test_wxid,
                "timestamp": int(time.time()),
                "is_self_message": False,
                "wxid": test_wxid,
                "uuid": "test_uuid_004",
                "push_content": "",
                "msg_source": "",
                "ver": 3,
                "msg_name": "AddMsgs"
            }
        ]
        
        # 发送测试消息
        queue_name = f"unhandled.{test_wxid}"
        
        # 队列参数
        queue_args = {
            "x-message-ttl": 60000,
            "x-expires": 3600000,
            "x-max-length": 3000,
            "x-overflow": "drop-head",
        }
        
        # 声明队列
        q = await ch.declare_queue(queue_name, durable=True, arguments=queue_args)
        
        for i, test_message in enumerate(test_messages, 1):
            # 发送消息
            message_body = json.dumps(test_message, ensure_ascii=False)
            message = aio_pika.Message(
                message_body.encode('utf-8'),
                content_type='application/json'
            )
            
            await ch.default_exchange.publish(message, routing_key=queue_name)
            
            logger.info(f"✅ 发送测试消息 {i}: {test_message['msg_type']} - {test_message['content'][:30]}...")
            
            # 间隔一下
            await asyncio.sleep(0.5)
        
        await conn.close()
        logger.info(f"✅ 成功发送 {len(test_messages)} 条测试消息")
        return True
        
    except Exception as e:
        logger.error(f"❌ 发送测试消息失败: {e}")
        return False


async def test_message_processing():
    """测试消息处理功能"""
    logger.info("🧪 测试消息处理功能")
    
    try:
        # 先发送测试消息
        logger.info("📤 发送测试消息...")
        if not await send_test_messages():
            logger.error("发送测试消息失败")
            return
        
        # 等待一下确保消息已发送
        await asyncio.sleep(2)
        
        # 导入应用程序
        from app import WechatApp
        
        # 创建应用程序
        app = WechatApp(
            group_name="YBA",
            use_mock_db=False,  # 使用真实数据库
            rate_limit_interval=1.0
        )
        
        logger.info(f"✅ 应用程序创建成功，成员数: {len(app.member_adapters)}")
        
        # 测试消息消费
        logger.info("🎯 开始消费测试消息...")
        
        # 创建一个任务来运行消费者
        consumer_task = asyncio.create_task(app._consume_wxid_messages())
        
        # 等待10秒让消费者处理消息
        logger.info("⏰ 等待10秒处理消息...")
        await asyncio.sleep(10)
        
        # 取消消费者任务
        consumer_task.cancel()
        try:
            await consumer_task
        except asyncio.CancelledError:
            logger.info("消费者任务已取消")
        
        app.shutdown()
        logger.info("✅ 消息处理测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


async def check_queue_status():
    """检查队列状态"""
    logger.info("\n🔍 检查队列状态")
    
    if not PIKA_AVAILABLE:
        logger.error("aio_pika不可用")
        return
    
    try:
        # 连接MQ
        mq_url = os.getenv('MQ_URL', '**********************************/')
        conn = await aio_pika.connect_robust(mq_url)
        ch = await conn.channel()
        
        # 检查队列
        queue_name = "unhandled.wxid_yba19990312"
        
        try:
            q = await ch.declare_queue(queue_name, passive=True)
            message_count = q.declaration_result.message_count
            consumer_count = q.declaration_result.consumer_count
            
            logger.info(f"📊 队列 {queue_name}:")
            logger.info(f"   消息数: {message_count}")
            logger.info(f"   消费者数: {consumer_count}")
            
        except Exception as e:
            logger.warning(f"⚠️ 队列 {queue_name} 不存在或无法访问: {e}")
        
        await conn.close()
        
    except Exception as e:
        logger.error(f"❌ 检查队列状态失败: {e}")


async def main():
    """主函数"""
    # 配置日志
    logger.remove()
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"
    )
    
    logger.info("🚀 消息处理功能测试")
    logger.info("=" * 50)
    
    # 检查环境变量
    enable_wxid = os.getenv('ENABLE_WXID_CONSUMER', 'false').lower() == 'true'
    logger.info(f"ENABLE_WXID_CONSUMER: {enable_wxid}")
    
    if not enable_wxid:
        logger.warning("⚠️ ENABLE_WXID_CONSUMER=false，请设置为true后重试")
        return
    
    # 检查队列状态
    await check_queue_status()
    
    # 测试消息处理
    await test_message_processing()
    
    # 再次检查队列状态
    await check_queue_status()
    
    logger.info("\n" + "=" * 50)
    logger.info("✅ 测试完成")


if __name__ == "__main__":
    asyncio.run(main())
