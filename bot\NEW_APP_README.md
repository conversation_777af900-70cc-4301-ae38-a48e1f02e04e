# 新版微信API应用程序 (app.py)

## 概述

新版 `app.py` 是一个重新设计的微信API应用程序，专注于单组多成员管理，提供了更精细的控制和更好的性能。

## 主要特性

### 1. 单组多成员管理
- ✅ 使用组名作为必需参数启动程序
- ✅ 从数据库查询该组所有成员，按ID排序
- ✅ 创建成员适配器列表，支持不同API版本混合使用

### 2. 智能成员选择和频率控制
- ✅ 默认使用ID最靠前的可用成员
- ✅ 支持指定特定成员进行API调用
- ✅ 单个成员API调用频率控制（默认1秒间隔）
- ✅ 智能故障转移：如果指定成员不可用，自动选择下一个可用成员
- ✅ 频率限制忽略：如果没有更多可用成员，忽略频率限制

### 3. 健康监控和故障转移
- ✅ 定期检查成员在线状态（每分钟）
- ✅ 使用在线状态API更新成员可用性
- ✅ 自动故障转移到可用成员
- ✅ 详细的统计信息和状态监控

### 4. 消息队列集成
- ✅ 接收商品匹配数据MQ消息
- ✅ 自动发送商品匹配通知到测试群
- ✅ 接收所有成员wxid的MQ消息并打印到控制台
- ✅ 支持异步消息处理

### 5. 完整的API接口
- ✅ 发送文本消息
- ✅ 发送图片消息
- ✅ 健康检查
- ✅ 状态查询
- ✅ 成员管理

## 使用方法

### 基本启动

```bash
# 启动YBA组的服务
python app.py YBA

# 使用模拟数据库
python app.py YBA --mock-db

# 设置频率限制为2秒
python app.py YBA --rate-limit 2.0

# 设置测试群ID
python app.py YBA --test-group "YBA-19990312"

# 设置日志级别
python app.py YBA --log-level DEBUG
```

### 使用启动脚本

```bash
# 使用便捷启动脚本
python start_group_app.py YBA
python start_group_app.py YBA --mock-db --rate-limit 2.0
```

### 程序化使用

```python
from app import WechatApp
from send_adapter.models import SendTextRequest

# 创建应用程序
app = WechatApp(
    group_name="YBA",
    use_mock_db=True,
    rate_limit_interval=1.0
)

# 发送文本消息（自动选择成员）
request = SendTextRequest(
    wxid="auto",
    to_wxid="target-group",
    content="Hello World!"
)
response = app.send_text(request)

# 发送文本消息（指定成员）
response = app.send_text(request, member_index=0)

# 获取状态
status = app.get_status()
print(f"可用成员: {status['available_count']}")

# 启动服务
app.start()
```

## 配置说明

### 环境变量

```bash
# 消息队列配置
MQ_URL=amqp://bot:bot@**************:5672/

# 测试群ID
TEST_GROUP_ID=YBA-19990312

# 数据库配置（如果不使用模拟模式）
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=password
DB_NAME=yba_ppmt
```

### 命令行参数

- `group`: 组名（必需）
- `--mock-db`: 使用模拟数据库
- `--rate-limit`: API调用频率限制间隔（秒）
- `--log-level`: 日志级别 (DEBUG/INFO/WARNING/ERROR)
- `--test-group`: 测试群ID

## 成员管理

### 成员选择策略

1. **自动选择**: 按ID升序选择第一个可用成员
2. **频率控制**: 检查成员是否在频率限制间隔内
3. **故障转移**: 如果当前成员不可用，自动选择下一个
4. **忽略限制**: 如果所有成员都在频率限制内，忽略限制选择可用成员

### 成员状态

- `is_online`: 通过在线状态API检查的在线状态
- `is_available`: 综合考虑账号状态和在线状态
- `success_rate`: 成功率统计
- `last_call_time`: 最后调用时间
- `last_health_check`: 最后健康检查时间

## 消息队列功能

### 商品匹配通知

应用程序会自动订阅 `events.product-matches` 交换机的消息，并将匹配结果发送到测试群。

### 未处理消息监控

应用程序会监控所有成员的 `unhandled.{wxid}` 队列，并将收到的消息打印到控制台。

## 监控和统计

### 实时统计

- 运行时间
- 处理消息数
- 发送成功数
- 错误数
- 健康检查次数

### 成员统计

- 总成员数
- 在线成员数
- 可用成员数
- 每个成员的详细状态

## 故障排除

### 常见问题

1. **没有可用成员**
   - 检查数据库中的成员配置
   - 确认成员的 `wechat_is_active` 和 `wechat_login_status` 状态

2. **API调用失败**
   - 检查成员的API配置（URL、Token等）
   - 查看日志中的详细错误信息

3. **消息队列连接失败**
   - 检查 `MQ_URL` 环境变量
   - 确认RabbitMQ服务可访问

### 调试技巧

```bash
# 启用详细日志
python app.py YBA --log-level DEBUG

# 使用模拟数据库进行测试
python app.py YBA --mock-db

# 运行测试脚本
python test_new_app.py
```

## 与旧版本的区别

| 功能 | 旧版本 | 新版本 |
|------|--------|--------|
| 组管理 | 多组管理 | 单组专注 |
| 成员选择 | 自动故障转移 | 精细控制+频率限制 |
| 频率控制 | 无 | 支持 |
| 健康检查 | 基础检查 | 在线状态API检查 |
| 消息队列 | 基础MQ | 商品匹配+未处理消息 |
| 启动方式 | 可选组名 | 必需组名 |

## 性能优化

- 异步消息处理
- 智能成员选择
- 频率控制避免API限制
- 连接复用和池化
- 内存使用优化
