#!/usr/bin/env python3
"""
启动指定组的微信API应用程序
使用新的单组多成员管理模式
"""
import sys
import os
from loguru import logger

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import WechatApp


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python start_group_app.py <组名> [选项]")
        print("示例: python start_group_app.py YBA")
        print("      python start_group_app.py YBA --mock-db --rate-limit 2.0")
        sys.exit(1)
    
    group_name = sys.argv[1]
    
    # 解析简单的命令行参数
    use_mock_db = "--mock-db" in sys.argv
    rate_limit = 1.0
    
    # 查找 --rate-limit 参数
    for i, arg in enumerate(sys.argv):
        if arg == "--rate-limit" and i + 1 < len(sys.argv):
            try:
                rate_limit = float(sys.argv[i + 1])
            except ValueError:
                logger.error("无效的频率限制值")
                sys.exit(1)
    
    # 配置日志
    logger.remove()
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    logger.info(f"🚀 启动组 {group_name} 的微信API应用程序")
    logger.info(f"   模拟数据库: {use_mock_db}")
    logger.info(f"   频率限制: {rate_limit}秒")
    
    try:
        # 创建应用程序
        app = WechatApp(
            group_name=group_name,
            use_mock_db=use_mock_db,
            rate_limit_interval=rate_limit
        )
        
        # 显示初始状态
        status = app.get_status()
        logger.info(f"📊 应用程序状态:")
        logger.info(f"   成员总数: {status['member_count']}")
        logger.info(f"   在线成员: {status['online_count']}")
        logger.info(f"   可用成员: {status['available_count']}")
        
        # 显示成员列表
        logger.info("👥 成员列表:")
        for member in status['members']:
            status_icon = "🟢" if member['is_available'] else "🔴"
            logger.info(f"   {status_icon} [{member['index']}] {member['name']} ({member['api_version']}) - {member['wxid']}")
        
        if status['available_count'] == 0:
            logger.error("❌ 没有可用的成员，无法启动服务")
            sys.exit(1)
        
        # 启动应用程序
        logger.info("🎯 启动服务...")
        app.start()
        
    except ValueError as e:
        logger.error(f"❌ 参数错误: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("👋 收到中断信号，正在关闭...")
    except Exception as e:
        logger.error(f"❌ 应用程序异常: {e}")
        sys.exit(1)
    finally:
        if 'app' in locals():
            app.shutdown()
        logger.info("✅ 应用程序已关闭")


if __name__ == "__main__":
    main()
