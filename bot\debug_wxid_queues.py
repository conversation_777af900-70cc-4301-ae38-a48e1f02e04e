#!/usr/bin/env python3
"""
调试wxid队列状态
"""
import os
import sys
import asyncio
from loguru import logger

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 加载 .env 文件
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

try:
    import aio_pika
    PIKA_AVAILABLE = True
except ImportError:
    PIKA_AVAILABLE = False
    logger.error("aio_pika not available")

from database import DatabaseManager


async def check_wxid_queues():
    """检查wxid队列状态"""
    if not PIKA_AVAILABLE:
        logger.error("❌ aio_pika不可用")
        return
    
    try:
        # 获取YBA组成员
        db_manager = DatabaseManager(use_mock=False)
        agents = db_manager.get_agents_by_group("YBA")
        db_manager.close()
        
        if not agents:
            logger.warning("没有找到YBA组成员")
            return
        
        logger.info(f"🔍 检查 {len(agents)} 个成员的wxid队列...")
        
        # 连接MQ
        mq_url = os.getenv('MQ_URL', '**********************************/')
        conn = await aio_pika.connect_robust(mq_url)
        ch = await conn.channel()
        
        existing_queues = []
        missing_queues = []
        error_queues = []
        
        for agent in agents:
            wxid = agent.wxid or f"auto_{agent.id}"
            queue_name = f"unhandled.{wxid}"
            
            try:
                # 检查队列是否存在
                q = await ch.declare_queue(queue_name, passive=True)
                message_count = q.declaration_result.message_count
                consumer_count = q.declaration_result.consumer_count
                
                existing_queues.append({
                    'agent': agent,
                    'wxid': wxid,
                    'queue_name': queue_name,
                    'message_count': message_count,
                    'consumer_count': consumer_count
                })
                
                logger.info(f"✅ {queue_name}: 消息={message_count}, 消费者={consumer_count}")
                
            except Exception as e:
                error_msg = str(e)
                if "NOT_FOUND" in error_msg:
                    missing_queues.append({
                        'agent': agent,
                        'wxid': wxid,
                        'queue_name': queue_name
                    })
                    logger.debug(f"📭 {queue_name}: 队列不存在")
                else:
                    error_queues.append({
                        'agent': agent,
                        'wxid': wxid,
                        'queue_name': queue_name,
                        'error': error_msg
                    })
                    logger.warning(f"❌ {queue_name}: {error_msg}")
        
        await conn.close()
        
        # 输出统计
        logger.info("\n📊 队列状态统计:")
        logger.info(f"   存在的队列: {len(existing_queues)}")
        logger.info(f"   缺失的队列: {len(missing_queues)}")
        logger.info(f"   错误的队列: {len(error_queues)}")
        
        # 显示存在的队列详情
        if existing_queues:
            logger.info("\n✅ 存在的队列:")
            for queue_info in existing_queues:
                agent = queue_info['agent']
                logger.info(f"   {queue_info['queue_name']}")
                logger.info(f"     成员: {agent.name} (ID: {agent.id})")
                logger.info(f"     消息数: {queue_info['message_count']}")
                logger.info(f"     消费者数: {queue_info['consumer_count']}")
        
        # 显示缺失的队列
        if missing_queues:
            logger.info("\n📭 缺失的队列:")
            for queue_info in missing_queues:
                agent = queue_info['agent']
                logger.info(f"   {queue_info['queue_name']}")
                logger.info(f"     成员: {agent.name} (ID: {agent.id})")
                logger.info(f"     wxid: {queue_info['wxid']}")
        
        # 显示错误的队列
        if error_queues:
            logger.info("\n❌ 错误的队列:")
            for queue_info in error_queues:
                agent = queue_info['agent']
                logger.info(f"   {queue_info['queue_name']}")
                logger.info(f"     成员: {agent.name} (ID: {agent.id})")
                logger.info(f"     错误: {queue_info['error']}")
        
        return {
            'existing': existing_queues,
            'missing': missing_queues,
            'error': error_queues
        }
        
    except Exception as e:
        logger.error(f"❌ 检查wxid队列失败: {e}")
        return None


async def test_consume_existing_queue():
    """测试消费一个存在的队列"""
    if not PIKA_AVAILABLE:
        return
    
    try:
        # 先检查队列状态
        queue_status = await check_wxid_queues()
        if not queue_status or not queue_status['existing']:
            logger.warning("没有找到可用的队列进行测试")
            return
        
        # 选择第一个存在的队列进行测试
        test_queue = queue_status['existing'][0]
        queue_name = test_queue['queue_name']
        wxid = test_queue['wxid']
        
        logger.info(f"\n🧪 测试消费队列: {queue_name}")
        
        # 连接MQ
        mq_url = os.getenv('MQ_URL', '**********************************/')
        conn = await aio_pika.connect_robust(mq_url)
        ch = await conn.channel()
        
        # 声明队列
        q = await ch.declare_queue(queue_name, passive=True)
        
        message_count = 0
        max_messages = 3  # 最多处理3条消息
        
        logger.info(f"开始消费消息，最多处理 {max_messages} 条...")
        
        async with q.iterator() as it:
            async for message in it:
                async with message.process():
                    try:
                        message_count += 1
                        body = message.body.decode('utf-8')
                        
                        logger.info(f"📨 收到消息 #{message_count}:")
                        logger.info(f"   队列: {queue_name}")
                        logger.info(f"   wxid: {wxid}")
                        logger.info(f"   消息长度: {len(body)} 字符")
                        
                        # 尝试解析JSON
                        try:
                            import json
                            data = json.loads(body)
                            logger.info(f"   消息ID: {data.get('msg_id', 'unknown')}")
                            logger.info(f"   发送者: {data.get('from_name', 'unknown')}")
                            logger.info(f"   内容: {data.get('content', 'unknown')[:100]}...")
                            logger.info(f"   时间: {data.get('timestamp', 'unknown')}")
                        except json.JSONDecodeError:
                            logger.warning(f"   JSON解析失败，原始内容: {body[:200]}...")
                        
                        if message_count >= max_messages:
                            logger.info(f"已处理 {max_messages} 条消息，停止测试")
                            break
                            
                    except Exception as e:
                        logger.error(f"处理消息异常: {e}")
        
        await conn.close()
        
        if message_count == 0:
            logger.info("队列中没有消息")
        else:
            logger.info(f"✅ 成功处理了 {message_count} 条消息")
        
    except Exception as e:
        logger.error(f"❌ 测试消费队列失败: {e}")


async def main():
    """主函数"""
    # 配置日志
    logger.remove()
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"
    )
    
    logger.info("🔍 wxid队列调试工具")
    logger.info("=" * 50)
    
    # 检查队列状态
    await check_wxid_queues()
    
    # 测试消费
    await test_consume_existing_queue()
    
    logger.info("\n" + "=" * 50)
    logger.info("✅ 调试完成")


if __name__ == "__main__":
    asyncio.run(main())
