#!/usr/bin/env python3
"""
测试账号状态判断逻辑
"""
import os
import sys
import time
from loguru import logger

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 加载 .env 文件
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

from app import WechatApp
from database import DatabaseManager


def test_account_status_logic():
    """测试账号状态判断逻辑"""
    logger.info("🧪 测试账号状态判断逻辑")
    
    try:
        # 创建应用程序
        app = WechatApp(
            group_name="YBA",
            use_mock_db=False,  # 使用真实数据库测试
            rate_limit_interval=1.0
        )
        
        logger.info(f"✅ 应用程序创建成功，成员数: {len(app.member_adapters)}")
        
        # 显示所有成员的状态
        logger.info("\n📊 成员状态详情:")
        for i, member in enumerate(app.member_adapters):
            account = member.account
            agent_id = account.extra_config.get('agent_id', 'unknown')
            
            logger.info(f"成员 [{i}]: {account.account_name}")
            logger.info(f"  Agent ID: {agent_id}")
            logger.info(f"  账号状态: {account.status}")
            logger.info(f"  在线状态: {member.is_online}")
            logger.info(f"  是否可用: {member.is_available()}")
            logger.info(f"  成功率: {account.get_success_rate():.2%}")
            logger.info(f"  错误次数: {account.error_count}")
            logger.info("")
        
        # 测试健康检查
        logger.info("🔍 执行健康检查...")
        import asyncio
        
        async def run_health_check():
            await app._check_member_health()
        
        # 运行健康检查
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(run_health_check())
        finally:
            loop.close()
        
        logger.info("✅ 健康检查完成")
        
        # 显示健康检查后的状态
        logger.info("\n📊 健康检查后的成员状态:")
        available_count = 0
        online_count = 0
        
        for i, member in enumerate(app.member_adapters):
            account = member.account
            agent_id = account.extra_config.get('agent_id', 'unknown')
            
            if member.is_online:
                online_count += 1
            if member.is_available():
                available_count += 1
            
            status_icon = "🟢" if member.is_available() else "🔴"
            online_icon = "📶" if member.is_online else "📵"
            
            logger.info(f"{status_icon} {online_icon} [{i}] {account.account_name}")
            logger.info(f"    Agent ID: {agent_id}")
            logger.info(f"    账号状态: {account.status}")
            logger.info(f"    在线状态: {member.is_online}")
            logger.info(f"    是否可用: {member.is_available()}")
            logger.info(f"    最后检查: {time.ctime(member.last_health_check) if member.last_health_check else 'Never'}")
            logger.info("")
        
        logger.info(f"📈 统计: 总数={len(app.member_adapters)}, 在线={online_count}, 可用={available_count}")
        
        # 测试数据库状态更新
        logger.info("\n🔄 验证数据库状态更新...")
        db_manager = DatabaseManager(use_mock=False)
        
        if not db_manager.use_mock:
            agents = db_manager.get_agents_by_group("YBA")
            logger.info("数据库中的登录状态:")
            for agent in agents:
                logger.info(f"  {agent.name} (ID: {agent.id}): login_status={agent.wechat_login_status}")
        else:
            logger.info("使用模拟数据库，跳过数据库状态验证")
        
        db_manager.close()
        app.shutdown()
        
        logger.info("✅ 账号状态测试完成")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_status_logic_explanation():
    """解释状态判断逻辑"""
    logger.info("\n📚 账号状态判断逻辑说明:")
    logger.info("=" * 60)
    
    logger.info("1. 账号初始状态判断:")
    logger.info("   - 只基于 wechat_is_active 字段")
    logger.info("   - wechat_is_active = false → DISABLED")
    logger.info("   - wechat_is_active = true → ACTIVE")
    logger.info("   - 不再使用 wechat_login_status 判断初始状态")
    
    logger.info("\n2. 在线状态检查:")
    logger.info("   - 每分钟调用在线状态API检查")
    logger.info("   - 检查成功 → is_online = true, 更新数据库 login_status = 1")
    logger.info("   - 检查失败 → is_online = false, 更新数据库 login_status = 0")
    
    logger.info("\n3. 账号可用性判断:")
    logger.info("   - account.is_available() AND member.is_online")
    logger.info("   - account.is_available() = (status == ACTIVE)")
    logger.info("   - member.is_online = 通过API检查得出的在线状态")
    
    logger.info("\n4. 动态状态更新:")
    logger.info("   - API调用成功 → 增加成功计数，ERROR状态恢复为ACTIVE")
    logger.info("   - API调用失败 → 增加错误计数，连续5次错误变为ERROR状态")
    
    logger.info("\n5. 数据库状态同步:")
    logger.info("   - wechat_login_status 由系统维护，反映实时在线状态")
    logger.info("   - wechat_is_active 由管理员控制，决定账号是否启用")
    
    logger.info("=" * 60)


def main():
    """主函数"""
    # 配置日志
    logger.remove()
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"
    )
    
    logger.info("🚀 账号状态判断逻辑测试")
    
    # 解释逻辑
    test_status_logic_explanation()
    
    # 测试实际逻辑
    test_account_status_logic()
    
    logger.info("\n✅ 测试完成")


if __name__ == "__main__":
    main()
