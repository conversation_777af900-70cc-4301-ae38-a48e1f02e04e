#!/usr/bin/env python3
"""
测试新的app.py功能
"""
import sys
import os
import time
from loguru import logger

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import WechatApp
from send_adapter.models import SendTextRequest


def test_app_initialization():
    """测试应用程序初始化"""
    logger.info("🧪 测试应用程序初始化")
    
    try:
        # 测试使用模拟数据库
        app = WechatApp(
            group_name="YBA",
            use_mock_db=True,
            rate_limit_interval=0.5
        )
        
        logger.info(f"✅ 应用程序初始化成功")
        logger.info(f"   组名: {app.group_name}")
        logger.info(f"   成员数: {len(app.member_adapters)}")
        logger.info(f"   频率限制: {app.rate_limit_interval}秒")
        
        # 获取状态
        status = app.get_status()
        logger.info(f"   状态: {status}")
        
        return app
        
    except Exception as e:
        logger.error(f"❌ 应用程序初始化失败: {e}")
        return None


def test_member_selection(app):
    """测试成员选择功能"""
    logger.info("\n🧪 测试成员选择功能")
    
    if not app or not app.member_adapters:
        logger.warning("没有可用的成员进行测试")
        return
    
    try:
        # 测试自动选择成员
        member = app._get_available_member()
        if member:
            logger.info(f"✅ 自动选择成员: {member.account.account_name}")
        else:
            logger.warning("❌ 没有可用成员")
        
        # 测试指定成员
        if len(app.member_adapters) > 0:
            member = app._get_available_member(0)
            if member:
                logger.info(f"✅ 指定成员[0]: {member.account.account_name}")
            else:
                logger.warning("❌ 指定成员[0]不可用")
        
    except Exception as e:
        logger.error(f"❌ 成员选择测试失败: {e}")


def test_api_call(app):
    """测试API调用功能"""
    logger.info("\n🧪 测试API调用功能")
    
    if not app:
        logger.warning("应用程序不可用")
        return
    
    try:
        # 创建测试请求
        request = SendTextRequest(
            wxid="auto",
            to_wxid="test-group",
            content="🧪 这是一个测试消息"
        )
        
        # 测试发送文本消息
        response = app.send_text(request)
        
        if response.ok:
            logger.info(f"✅ API调用成功: {response.data}")
        else:
            logger.warning(f"⚠️ API调用失败: {response.message}")
        
    except Exception as e:
        logger.error(f"❌ API调用测试失败: {e}")


def test_rate_limiting(app):
    """测试频率限制功能"""
    logger.info("\n🧪 测试频率限制功能")
    
    if not app or not app.member_adapters:
        logger.warning("没有可用的成员进行测试")
        return
    
    try:
        member = app.member_adapters[0]
        
        # 第一次调用
        can_call_1 = member.can_call_now()
        logger.info(f"第一次调用检查: {can_call_1}")
        
        # 标记调用
        member.mark_call()
        
        # 立即检查
        can_call_2 = member.can_call_now()
        logger.info(f"立即检查: {can_call_2}")
        
        # 等待一段时间后检查
        time.sleep(0.6)  # 等待超过频率限制间隔
        can_call_3 = member.can_call_now()
        logger.info(f"等待后检查: {can_call_3}")
        
        if not can_call_2 and can_call_3:
            logger.info("✅ 频率限制功能正常")
        else:
            logger.warning("⚠️ 频率限制功能异常")
        
    except Exception as e:
        logger.error(f"❌ 频率限制测试失败: {e}")


def main():
    """主函数"""
    # 配置日志
    logger.remove()
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"
    )
    
    logger.info("🚀 开始测试新的app.py功能")
    logger.info("=" * 50)
    
    # 测试应用程序初始化
    app = test_app_initialization()
    
    # 测试成员选择
    test_member_selection(app)
    
    # 测试API调用
    test_api_call(app)
    
    # 测试频率限制
    test_rate_limiting(app)
    
    logger.info("\n" + "=" * 50)
    logger.info("🏁 测试完成")
    
    # 清理
    if app:
        app.shutdown()


if __name__ == "__main__":
    main()
