#!/usr/bin/env python3
"""
测试应用程序关闭功能
"""
import os
import sys
import time
import signal
import threading
from loguru import logger

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 加载 .env 文件
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

from app import WechatApp


def test_shutdown():
    """测试关闭功能"""
    logger.info("🧪 测试应用程序关闭功能")
    
    try:
        # 创建应用程序
        app = WechatApp(
            group_name="YBA",
            use_mock_db=True,  # 使用模拟数据库进行测试
            rate_limit_interval=1.0
        )
        
        logger.info("✅ 应用程序创建成功")
        
        # 在单独线程中启动应用程序
        def start_app():
            try:
                app.start()
            except Exception as e:
                logger.error(f"应用程序启动异常: {e}")
        
        app_thread = threading.Thread(target=start_app, daemon=True)
        app_thread.start()
        
        # 等待应用程序启动
        time.sleep(3)
        
        if app.is_running:
            logger.info("✅ 应用程序启动成功")
            
            # 等待5秒后发送关闭信号
            logger.info("⏰ 5秒后将发送关闭信号...")
            time.sleep(5)
            
            logger.info("📤 发送关闭信号...")
            app.shutdown()
            
            # 等待关闭完成
            start_time = time.time()
            while app.is_running and time.time() - start_time < 10:
                time.sleep(0.1)
            
            if not app.is_running:
                logger.info("✅ 应用程序成功关闭")
            else:
                logger.error("❌ 应用程序关闭超时")
        else:
            logger.error("❌ 应用程序启动失败")
            
    except Exception as e:
        logger.error(f"❌ 测试异常: {e}")


def test_signal_shutdown():
    """测试信号关闭功能"""
    logger.info("\n🧪 测试信号关闭功能")
    
    try:
        # 创建应用程序
        app = WechatApp(
            group_name="YBA",
            use_mock_db=True,
            rate_limit_interval=1.0
        )
        
        # 在单独线程中启动应用程序
        def start_app():
            try:
                app.start()
            except KeyboardInterrupt:
                logger.info("收到键盘中断")
            except Exception as e:
                logger.error(f"应用程序启动异常: {e}")
        
        app_thread = threading.Thread(target=start_app, daemon=True)
        app_thread.start()
        
        # 等待应用程序启动
        time.sleep(3)
        
        if app.is_running:
            logger.info("✅ 应用程序启动成功")
            
            # 等待3秒后发送SIGINT信号
            logger.info("⏰ 3秒后将发送SIGINT信号...")
            time.sleep(3)
            
            logger.info("📤 发送SIGINT信号...")
            os.kill(os.getpid(), signal.SIGINT)
            
            # 等待关闭完成
            start_time = time.time()
            while app.is_running and time.time() - start_time < 10:
                time.sleep(0.1)
            
            if not app.is_running:
                logger.info("✅ 应用程序成功响应信号关闭")
            else:
                logger.error("❌ 应用程序信号关闭超时")
        else:
            logger.error("❌ 应用程序启动失败")
            
    except KeyboardInterrupt:
        logger.info("✅ 成功捕获键盘中断信号")
    except Exception as e:
        logger.error(f"❌ 测试异常: {e}")


def main():
    """主函数"""
    # 配置日志
    logger.remove()
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"
    )
    
    logger.info("🚀 开始关闭功能测试")
    logger.info("=" * 50)
    
    # 测试正常关闭
    test_shutdown()
    
    # 等待一下
    time.sleep(2)
    
    # 测试信号关闭
    test_signal_shutdown()
    
    logger.info("\n" + "=" * 50)
    logger.info("✅ 关闭功能测试完成")


if __name__ == "__main__":
    main()
