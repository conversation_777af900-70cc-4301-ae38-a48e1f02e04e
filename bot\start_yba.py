#!/usr/bin/env python3
"""
YBA组专用启动脚本
兼容旧版本的启动方式，使用新的WechatApp架构
"""
import os
import sys
import time
from loguru import logger

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 加载 .env 文件
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    logger.warning("python-dotenv not available, using system environment variables only")

from app import WechatApp
from database import DatabaseManager


def print_banner():
    """打印启动横幅"""
    print("╔══════════════════════════════════════════════════════════════╗")
    print("║                    YBA组微信API服务                          ║")
    print("║                                                              ║")
    print("║  🚀 多账号故障转移                                           ║")
    print("║  📱 实时消息处理                                             ║")
    print("║  🔄 自动健康检查                                             ║")
    print("║  📊 详细统计监控                                             ║")
    print("║                                                              ║")
    print("║  按 Ctrl+C 停止服务                                          ║")
    print("╚══════════════════════════════════════════════════════════════╝")
    print()


def check_prerequisites():
    """检查运行环境"""
    logger.info("🔍 检查运行环境...")

    # 从环境变量读取配置
    use_mock_db = os.getenv('USE_MOCK_DB', 'false').lower() == 'true'

    # 检查数据库连接
    try:
        # 根据环境变量决定是否使用模拟数据库
        db_manager = DatabaseManager(use_mock=use_mock_db)

        if not db_manager.use_mock:
            logger.info("✅ 数据库连接成功")
            # 检查YBA组配置
            agents = db_manager.get_agents_by_group("YBA")
            if agents:
                logger.info(f"✅ 找到 {len(agents)} 个YBA组账号配置")
                for agent in agents[:3]:  # 显示前3个账号信息
                    logger.info(f"   - {agent.name} (ID: {agent.id}, API: {agent.api_version})")
                if len(agents) > 3:
                    logger.info(f"   ... 还有 {len(agents) - 3} 个账号")
            else:
                logger.warning("⚠️ 未找到YBA组账号配置")
        else:
            logger.info("ℹ️ 使用模拟数据库模式")
            agents = db_manager.get_agents_by_group("YBA")
            logger.info(f"✅ 找到 {len(agents)} 个YBA组账号配置")

        db_manager.close()

    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        logger.info("ℹ️ 将强制使用模拟数据库模式")
        return False

    logger.info("✅ 运行环境检查通过")
    return True


def main():
    """主函数"""
    # 配置日志
    logger.remove()
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # 打印横幅
    print_banner()
    
    # 检查运行环境
    if not check_prerequisites():
        logger.error("❌ 运行环境检查失败")
        sys.exit(1)
    
    logger.info("🚀 启动YBA组微信API服务...")
    
    try:
        # 从环境变量读取配置
        use_mock_db = os.getenv('USE_MOCK_DB', 'false').lower() == 'true'

        # 创建应用程序（兼容旧版本参数）
        # 注意：新版本不再需要 use_mock_mq 参数
        app = WechatApp(
            group_name="YBA",
            use_mock_db=use_mock_db,
            rate_limit_interval=1.0
        )
        
        # 显示初始状态
        status = app.get_status()
        logger.info("📊 应用程序状态:")
        logger.info(f"   成员总数: {status['member_count']}")
        logger.info(f"   在线成员: {status['online_count']}")
        logger.info(f"   可用成员: {status['available_count']}")
        
        # 显示成员列表
        logger.info("👥 成员列表:")
        for member in status['members']:
            status_icon = "🟢" if member['is_available'] else "🔴"
            logger.info(f"   {status_icon} [{member['index']}] {member['name']} ({member['api_version']}) - {member['wxid']}")
        
        if status['available_count'] == 0:
            logger.error("❌ 没有可用的成员，无法启动服务")
            sys.exit(1)
        
        # 启动应用程序
        logger.info("🎯 启动服务...")
        app.start()
        
    except Exception as e:
        logger.error(f"启动YBA组服务失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("👋 收到中断信号，正在关闭...")
    finally:
        if 'app' in locals():
            app.shutdown()
        logger.info("✅ YBA组服务已关闭")


if __name__ == "__main__":
    main()
