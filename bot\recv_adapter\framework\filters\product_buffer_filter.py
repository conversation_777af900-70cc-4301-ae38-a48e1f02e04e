"""
产品消息缓冲过滤器 - 防止5分钟内发送相同store中的相同product
"""
import time
from typing import Dict, List, Set
from collections import OrderedDict
from loguru import logger


class ProductBufferFilter:
    """
    产品消息缓冲过滤器
    
    防止在指定时间窗口内重复发送相同store中的相同product到MQ
    使用内存缓存，支持自动清理过期记录
    """
    
    def __init__(self, buffer_window_seconds: int = 300, clean_interval_seconds: int = 60, 
                 clean_threshold: int = 1000):
        """
        初始化产品缓冲过滤器
        
        Args:
            buffer_window_seconds: 缓冲窗口大小（秒），默认300秒（5分钟）
            clean_interval_seconds: 清理间隔（秒），默认60秒
            clean_threshold: 缓冲区大小超过此值时强制清理，默认1000
        """
        self.buffer_window = buffer_window_seconds
        self.clean_interval = clean_interval_seconds
        self.clean_threshold = clean_threshold
        
        # 使用OrderedDict维护插入顺序，便于清理
        # 格式: {f"{store_id}_{product_id}": timestamp}
        self.buffer: OrderedDict[str, float] = OrderedDict()
        
        # 记录上次清理时间
        self.last_clean_time = time.time()
        
        logger.info(f"ProductBufferFilter initialized: window={buffer_window_seconds}s, "
                   f"clean_interval={clean_interval_seconds}s, threshold={clean_threshold}")
    
    def should_send_product(self, store_id: int, product_id: int) -> bool:
        """
        检查是否应该发送产品消息到MQ
        
        Args:
            store_id: 店铺ID
            product_id: 产品ID
            
        Returns:
            bool: 如果应该发送返回True，否则返回False
        """
        current_time = time.time()
        buffer_key = f"{store_id}_{product_id}"
        
        # 检查是否需要清理缓冲区
        self._check_and_clean_buffer(current_time)
        
        # 检查是否在缓冲期内
        if buffer_key in self.buffer:
            last_send_time = self.buffer[buffer_key]
            time_diff = current_time - last_send_time
            
            if time_diff < self.buffer_window:
                logger.debug(f"Product message filtered by buffer: store_id={store_id}, "
                           f"product_id={product_id}, time_since_last={time_diff:.1f}s")
                return False
        
        # 添加到缓冲区（更新时间戳）
        self.buffer[buffer_key] = current_time
        
        logger.debug(f"Product message allowed: store_id={store_id}, product_id={product_id}")
        return True
    
    def should_send_products_batch(self, store_id: int, product_ids: List[int]) -> List[int]:
        """
        批量检查多个产品是否应该发送到MQ
        
        Args:
            store_id: 店铺ID
            product_ids: 产品ID列表
            
        Returns:
            List[int]: 应该发送的产品ID列表
        """
        allowed_products = []
        
        for product_id in product_ids:
            if self.should_send_product(store_id, product_id):
                allowed_products.append(product_id)
        
        if len(allowed_products) != len(product_ids):
            filtered_count = len(product_ids) - len(allowed_products)
            logger.info(f"Filtered {filtered_count} products from batch for store {store_id}")
        
        return allowed_products
    
    def _check_and_clean_buffer(self, current_time: float) -> None:
        """
        检查并清理缓冲区
        
        Args:
            current_time: 当前时间戳
        """
        # 检查是否需要清理
        need_clean = False
        
        # 周期性清理
        if current_time - self.last_clean_time > self.clean_interval:
            need_clean = True
        
        # 缓冲区大小超过阈值时强制清理
        elif len(self.buffer) > self.clean_threshold:
            need_clean = True
            logger.debug(f"Buffer size ({len(self.buffer)}) exceeded threshold ({self.clean_threshold}), "
                        "forcing cleanup")
        
        if need_clean:
            self._clean_expired_entries(current_time)
            self.last_clean_time = current_time
    
    def _clean_expired_entries(self, current_time: float) -> None:
        """
        清理过期的缓冲记录
        
        Args:
            current_time: 当前时间戳
        """
        # 找出过期的键
        expired_keys = [
            key for key, timestamp in self.buffer.items()
            if current_time - timestamp > self.buffer_window
        ]
        
        # 删除过期记录
        for key in expired_keys:
            del self.buffer[key]
        
        if expired_keys:
            logger.debug(f"Cleaned {len(expired_keys)} expired entries from product buffer")
    
    def get_buffer_stats(self) -> Dict[str, any]:
        """
        获取缓冲区统计信息
        
        Returns:
            Dict: 包含缓冲区大小、配置等信息的字典
        """
        current_time = time.time()
        
        # 统计活跃记录数（未过期的）
        active_count = sum(
            1 for timestamp in self.buffer.values()
            if current_time - timestamp <= self.buffer_window
        )
        
        return {
            "total_entries": len(self.buffer),
            "active_entries": active_count,
            "buffer_window_seconds": self.buffer_window,
            "clean_interval_seconds": self.clean_interval,
            "clean_threshold": self.clean_threshold,
            "last_clean_time": self.last_clean_time,
            "time_since_last_clean": current_time - self.last_clean_time
        }
    
    def clear_buffer(self) -> None:
        """清空缓冲区"""
        cleared_count = len(self.buffer)
        self.buffer.clear()
        self.last_clean_time = time.time()
        logger.info(f"Cleared product buffer: {cleared_count} entries removed")
    
    def remove_store_products(self, store_id: int) -> None:
        """
        移除指定店铺的所有产品缓冲记录
        
        Args:
            store_id: 店铺ID
        """
        store_prefix = f"{store_id}_"
        keys_to_remove = [key for key in self.buffer.keys() if key.startswith(store_prefix)]
        
        for key in keys_to_remove:
            del self.buffer[key]
        
        if keys_to_remove:
            logger.info(f"Removed {len(keys_to_remove)} buffer entries for store {store_id}")
    
    def remove_product_from_all_stores(self, product_id: int) -> None:
        """
        从所有店铺中移除指定产品的缓冲记录
        
        Args:
            product_id: 产品ID
        """
        product_suffix = f"_{product_id}"
        keys_to_remove = [key for key in self.buffer.keys() if key.endswith(product_suffix)]
        
        for key in keys_to_remove:
            del self.buffer[key]
        
        if keys_to_remove:
            logger.info(f"Removed {len(keys_to_remove)} buffer entries for product {product_id}")


# 全局产品缓冲过滤器实例
_global_product_buffer_filter: ProductBufferFilter = None


def init_product_buffer_filter(buffer_window_seconds: int = 300, 
                              clean_interval_seconds: int = 60,
                              clean_threshold: int = 1000) -> ProductBufferFilter:
    """
    初始化全局产品缓冲过滤器
    
    Args:
        buffer_window_seconds: 缓冲窗口大小（秒）
        clean_interval_seconds: 清理间隔（秒）
        clean_threshold: 清理阈值
        
    Returns:
        ProductBufferFilter: 过滤器实例
    """
    global _global_product_buffer_filter
    _global_product_buffer_filter = ProductBufferFilter(
        buffer_window_seconds=buffer_window_seconds,
        clean_interval_seconds=clean_interval_seconds,
        clean_threshold=clean_threshold
    )
    return _global_product_buffer_filter


def get_product_buffer_filter() -> ProductBufferFilter:
    """
    获取全局产品缓冲过滤器实例
    
    Returns:
        ProductBufferFilter: 过滤器实例，如果未初始化则返回None
    """
    return _global_product_buffer_filter


def should_send_product(store_id: int, product_id: int) -> bool:
    """
    便捷函数：检查是否应该发送产品消息
    
    Args:
        store_id: 店铺ID
        product_id: 产品ID
        
    Returns:
        bool: 如果应该发送返回True，否则返回False
    """
    filter_instance = get_product_buffer_filter()
    if filter_instance is None:
        logger.warning("Product buffer filter not initialized, allowing all messages")
        return True
    
    return filter_instance.should_send_product(store_id, product_id)


def should_send_products_batch(store_id: int, product_ids: List[int]) -> List[int]:
    """
    便捷函数：批量检查多个产品是否应该发送
    
    Args:
        store_id: 店铺ID
        product_ids: 产品ID列表
        
    Returns:
        List[int]: 应该发送的产品ID列表
    """
    filter_instance = get_product_buffer_filter()
    if filter_instance is None:
        logger.warning("Product buffer filter not initialized, allowing all messages")
        return product_ids
    
    return filter_instance.should_send_products_batch(store_id, product_ids)
