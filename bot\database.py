"""
数据库管理器
处理与MySQL数据库的连接和查询操作
"""
import os
import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from loguru import logger

# 加载 .env 文件
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    logger.warning("python-dotenv not available, using system environment variables only")

try:
    import pymysql
    PYMYSQL_AVAILABLE = True
except ImportError:
    PYMYSQL_AVAILABLE = False
    logger.warning("pymysql not available, using mock data mode")


@dataclass
class AgentConfig:
    """代理商配置数据类"""
    id: int
    group: str
    name: str
    contact: Optional[str]
    token: Optional[str]
    wechat_base_url: Optional[str]
    wechat_is_active: bool
    wechat_login_status: int
    api_version: str
    wxid: Optional[str]
    device_id: Optional[str]
    app_id: Optional[str]
    last_check_time: Optional[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'group': self.group,
            'name': self.name,
            'contact': self.contact,
            'token': self.token,
            'wechat_base_url': self.wechat_base_url,
            'wechat_is_active': self.wechat_is_active,
            'wechat_login_status': self.wechat_login_status,
            'api_version': self.api_version,
            'wxid': self.wxid,
            'device_id': self.device_id,
            'app_id': self.app_id,
            'last_check_time': self.last_check_time
        }


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, use_mock: bool = None):
        """
        初始化数据库管理器
        
        Args:
            use_mock: 是否使用模拟数据，None时自动检测
        """
        self.use_mock = use_mock if use_mock is not None else not PYMYSQL_AVAILABLE
        self.connection = None
        
        if not self.use_mock:
            self._init_database_connection()
        else:
            logger.info("使用模拟数据模式")
    
    def _init_database_connection(self):
        """初始化数据库连接"""
        try:
            self.connection = pymysql.connect(
                host=os.getenv('DB_HOST', 'localhost'),
                port=int(os.getenv('DB_PORT', 3306)),
                user=os.getenv('DB_USER', 'root'),
                password=os.getenv('DB_PASSWORD', ''),
                database=os.getenv('DB_NAME', 'yba_ppmt'),
                charset='utf8mb4',
                autocommit=True
            )
            logger.info("数据库连接成功")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            logger.info("切换到模拟数据模式")
            self.use_mock = True
    
    def _get_mock_data(self) -> List[AgentConfig]:
        """获取模拟数据"""
        mock_agents = [
            AgentConfig(
                id=1,
                group="YBA",
                name="YBA主账号",
                contact="<EMAIL>",
                token="1f9f8077-d47b-4f64-9227-61eed57c225d",
                wechat_base_url="http://api.geweapi.com/gewe/v2/api",
                wechat_is_active=True,
                wechat_login_status=1,
                api_version="v3",
                wxid="wxid_yba19990312",
                device_id=None,
                app_id="wx_SyxIy_9ZUpnIxmw83Y6Hl",
                last_check_time="2025-08-19 19:30:00"
            ),
            AgentConfig(
                id=2,
                group="YBA",
                name="YBA备用V1",
                contact="<EMAIL>",
                token="backup_v1_token",
                wechat_base_url="http://8.133.252.208:8080",
                wechat_is_active=True,
                wechat_login_status=1,
                api_version="v1",
                wxid="wxid_yba_backup_v1",
                device_id=None,
                app_id=None,
                last_check_time="2025-08-19 19:25:00"
            ),
            AgentConfig(
                id=3,
                group="YBA",
                name="YBA备用V2",
                contact="<EMAIL>",
                token=None,  # V2不需要token
                wechat_base_url="http://8.133.252.208:8060/api",
                wechat_is_active=True,
                wechat_login_status=0,  # 模拟离线状态
                api_version="v2",
                wxid="wxid_yba_backup_v2",
                device_id="device_yba_v2",
                app_id=None,
                last_check_time="2025-08-19 19:20:00"
            ),
            AgentConfig(
                id=10,
                group="TEST",
                name="测试账号1",
                contact="<EMAIL>",
                token="test_token_1",
                wechat_base_url="http://test.api.com",
                wechat_is_active=True,
                wechat_login_status=1,
                api_version="v1",
                wxid="wxid_test1",
                device_id=None,
                app_id=None,
                last_check_time="2025-08-19 19:30:00"
            ),
            AgentConfig(
                id=11,
                group="TEST",
                name="测试账号2",
                contact="<EMAIL>",
                token="test_token_2",
                wechat_base_url="http://test2.api.com",
                wechat_is_active=False,  # 模拟未激活
                wechat_login_status=0,
                api_version="v2",
                wxid="wxid_test2",
                device_id="device_test2",
                app_id=None,
                last_check_time="2025-08-19 19:15:00"
            )
        ]
        return mock_agents
    
    def get_agents_by_group(self, group: str) -> List[AgentConfig]:
        """
        根据组名获取代理商配置
        
        Args:
            group: 组名
            
        Returns:
            代理商配置列表，按ID升序排列
        """
        if self.use_mock:
            mock_data = self._get_mock_data()
            return [agent for agent in mock_data if agent.group == group]
        
        try:
            with self.connection.cursor(pymysql.cursors.DictCursor) as cursor:
                sql = """
                SELECT id, `group`, name, contact, token, wechat_base_url, 
                       wechat_is_active, wechat_login_status, api_version, 
                       wxid, device_id, app_id, last_check_time
                FROM agents 
                WHERE `group` = %s AND wechat_is_active = 1
                ORDER BY id ASC
                """
                cursor.execute(sql, (group,))
                results = cursor.fetchall()
                
                agents = []
                for row in results:
                    agent = AgentConfig(
                        id=row['id'],
                        group=row['group'],
                        name=row['name'],
                        contact=row['contact'],
                        token=row['token'],
                        wechat_base_url=row['wechat_base_url'],
                        wechat_is_active=bool(row['wechat_is_active']),
                        wechat_login_status=row['wechat_login_status'],
                        api_version=row['api_version'] or 'v1',
                        wxid=row['wxid'],
                        device_id=row['device_id'],
                        app_id=row['app_id'],
                        last_check_time=str(row['last_check_time']) if row['last_check_time'] else None
                    )
                    agents.append(agent)
                
                logger.info(f"从数据库获取到 {len(agents)} 个 {group} 组的代理商配置")
                return agents
                
        except Exception as e:
            logger.error(f"查询数据库失败: {e}")
            return []
    
    def update_login_status(self, agent_id: int, status: int) -> bool:
        """
        更新代理商登录状态
        
        Args:
            agent_id: 代理商ID
            status: 登录状态 (0=未登录, 1=已登录)
            
        Returns:
            是否更新成功
        """
        if self.use_mock:
            logger.info(f"模拟模式：更新代理商 {agent_id} 登录状态为 {status}")
            return True
        
        try:
            with self.connection.cursor() as cursor:
                sql = """
                UPDATE agents 
                SET wechat_login_status = %s, last_check_time = NOW()
                WHERE id = %s
                """
                cursor.execute(sql, (status, agent_id))
                
                if cursor.rowcount > 0:
                    logger.info(f"更新代理商 {agent_id} 登录状态为 {status}")
                    return True
                else:
                    logger.warning(f"代理商 {agent_id} 不存在")
                    return False
                    
        except Exception as e:
            logger.error(f"更新登录状态失败: {e}")
            return False
    
    def get_all_groups(self) -> List[str]:
        """获取所有组名"""
        if self.use_mock:
            mock_data = self._get_mock_data()
            return list(set(agent.group for agent in mock_data))
        
        try:
            with self.connection.cursor() as cursor:
                sql = "SELECT DISTINCT `group` FROM agents WHERE wechat_is_active = 1"
                cursor.execute(sql)
                results = cursor.fetchall()
                return [row[0] for row in results if row[0]]
                
        except Exception as e:
            logger.error(f"查询组列表失败: {e}")
            return []
    
    def close(self):
        """关闭数据库连接"""
        if self.connection and not self.use_mock:
            self.connection.close()
            logger.info("数据库连接已关闭")


# 全局数据库管理器实例
db_manager = DatabaseManager()
