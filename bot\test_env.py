#!/usr/bin/env python3
"""
测试环境变量加载
"""
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 加载 .env 文件
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ 成功加载 .env 文件")
except ImportError:
    print("❌ python-dotenv 不可用")

# 测试数据库环境变量
print("\n📊 数据库配置:")
print(f"DB_HOST: {os.getenv('DB_HOST', 'NOT_SET')}")
print(f"DB_PORT: {os.getenv('DB_PORT', 'NOT_SET')}")
print(f"DB_USER: {os.getenv('DB_USER', 'NOT_SET')}")
print(f"DB_PASSWORD: {'***' if os.getenv('DB_PASSWORD') else 'NOT_SET'}")
print(f"DB_NAME: {os.getenv('DB_NAME', 'NOT_SET')}")
print(f"USE_MOCK_DB: {os.getenv('USE_MOCK_DB', 'NOT_SET')}")

# 测试消息队列环境变量
print("\n📨 消息队列配置:")
print(f"MQ_HOST: {os.getenv('MQ_HOST', 'NOT_SET')}")
print(f"MQ_PORT: {os.getenv('MQ_PORT', 'NOT_SET')}")
print(f"MQ_USERNAME: {os.getenv('MQ_USERNAME', 'NOT_SET')}")
print(f"MQ_PASSWORD: {'***' if os.getenv('MQ_PASSWORD') else 'NOT_SET'}")

# 测试数据库连接
print("\n🔗 测试数据库连接:")
try:
    from database import DatabaseManager
    
    # 测试真实数据库连接
    db = DatabaseManager(use_mock=False)
    if db.use_mock:
        print("⚠️ 自动切换到模拟数据模式")
    else:
        print("✅ 数据库连接成功")
        
        # 测试查询
        groups = db.get_all_groups()
        print(f"📋 找到组: {groups}")
        
        if groups:
            agents = db.get_agents_by_group(groups[0])
            print(f"👥 {groups[0]} 组成员数: {len(agents)}")
    
    db.close()
    
except Exception as e:
    print(f"❌ 数据库连接失败: {e}")

print("\n✅ 环境变量测试完成")
