#!/usr/bin/env python3
"""
微信API应用程序主入口
支持单组多成员管理、频率控制、故障转移和消息队列处理
"""
import os
import sys
import signal
import argparse
import time
import threading
import asyncio
import json
from typing import Dict, Any, List, Optional
from loguru import logger

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 加载 .env 文件
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    logger.warning("python-dotenv not available, using system environment variables only")

from database import DatabaseManager, AgentConfig
from send_adapter.account import WechatAccount, AccountStatus
from send_adapter.factory import create_adapter_from_account
from send_adapter.models import SendTextRequest, SendImageRequest, APIResponse, GetOnlineStatusRequest

try:
    import aio_pika
    PIKA_AVAILABLE = True
except ImportError:
    PIKA_AVAILABLE = False
    logger.warning("aio_pika not available, MQ features disabled")


class MemberAdapter:
    """单个成员的适配器包装器，包含频率控制"""

    def __init__(self, account: WechatAccount):
        self.account = account
        self.adapter = create_adapter_from_account(account)
        self.last_call_time = 0
        self.rate_limit_interval = 1.0  # 默认1秒间隔
        self.is_online = True
        self.last_health_check = 0

    def can_call_now(self) -> bool:
        """检查是否可以立即调用API"""
        return time.time() - self.last_call_time >= self.rate_limit_interval

    def mark_call(self):
        """标记API调用"""
        self.last_call_time = time.time()

    def is_available(self) -> bool:
        """检查成员是否可用"""
        return self.account.is_available() and self.is_online


class WechatApp:
    """微信API应用程序 - 单组多成员管理"""

    def __init__(self, group_name: str, use_mock_db: bool = None, rate_limit_interval: float = 1.0, use_mock_mq: bool = None):
        """
        初始化应用程序

        Args:
            group_name: 组名（必需）
            use_mock_db: 是否使用模拟数据库，None时从环境变量读取
            rate_limit_interval: API调用频率限制间隔（秒）
            use_mock_mq: 兼容参数，已废弃（新版本不再使用消息队列管理器）
        """
        if not group_name:
            raise ValueError("组名是必需的参数")

        # 兼容性警告
        if use_mock_mq is not None:
            logger.warning("use_mock_mq 参数已废弃，新版本使用直接的MQ连接方式")

        # 从环境变量读取配置（如果未指定）
        if use_mock_db is None:
            use_mock_db = os.getenv('USE_MOCK_DB', 'false').lower() == 'true'

        self.group_name = group_name
        self.rate_limit_interval = rate_limit_interval
        self.is_running = False
        self.shutdown_event = threading.Event()

        # 初始化数据库
        self.db_manager = DatabaseManager(use_mock=use_mock_db)

        # 成员适配器列表（按ID排序）
        self.member_adapters: List[MemberAdapter] = []
        self.current_member_index = 0

        # MQ连接配置
        self.mq_url = os.getenv('MQ_URL', '**********************************/')
        self.test_group_id = os.getenv('TEST_GROUP_ID', 'YBA-19990312')  # 测试群ID

        # 功能开关
        self.enable_wxid_consumer = os.getenv('ENABLE_WXID_CONSUMER', 'true').lower() == 'true'

        # 统计信息
        self.stats = {
            'start_time': time.time(),
            'messages_processed': 0,
            'messages_sent': 0,
            'errors': 0,
            'health_checks': 0
        }

        # 初始化成员
        self._load_members()

        logger.info(f"微信API应用程序初始化完成，组: {group_name}, 成员数: {len(self.member_adapters)}")

    def _load_members(self):
        """从数据库加载组成员，按ID排序"""
        try:
            # 获取组的所有成员配置
            agent_configs = self.db_manager.get_agents_by_group(self.group_name)

            if not agent_configs:
                logger.warning(f"组 {self.group_name} 没有找到任何成员")
                return

            # 按ID排序
            agent_configs.sort(key=lambda x: x.id)

            self.member_adapters = []
            for config in agent_configs:
                try:
                    # 创建账号对象
                    account = self._create_account_from_config(config)
                    if account:
                        # 创建成员适配器
                        member_adapter = MemberAdapter(account)
                        member_adapter.rate_limit_interval = self.rate_limit_interval
                        self.member_adapters.append(member_adapter)

                        logger.info(f"加载成员: {config.name} (ID: {config.id}, API: {config.api_version})")

                except Exception as e:
                    logger.error(f"创建成员适配器失败 {config.name}: {e}")

            logger.info(f"成功加载 {len(self.member_adapters)} 个成员")

        except Exception as e:
            logger.error(f"加载组成员失败: {e}")

    def _create_account_from_config(self, config: AgentConfig) -> Optional[WechatAccount]:
        """从配置创建账号对象"""
        try:
            # 确定账号状态（只基于管理员设置的激活状态）
            if not config.wechat_is_active:
                status = AccountStatus.DISABLED
            else:
                status = AccountStatus.ACTIVE

            # 根据API版本创建账号
            if config.api_version == "v1":
                account = WechatAccount.create_v1_account(
                    account_id=str(config.id),
                    account_name=config.name,
                    wxid=config.wxid or f"auto_{config.id}",
                    base_url=config.wechat_base_url,
                    api_key=config.token,
                    priority=config.id  # 使用ID作为优先级
                )
            elif config.api_version == "v2":
                account = WechatAccount.create_v2_account(
                    account_id=str(config.id),
                    account_name=config.name,
                    wxid=config.wxid or f"auto_{config.id}",
                    base_url=config.wechat_base_url,
                    api_key=config.token,
                    priority=config.id
                )
            elif config.api_version == "v3":
                account = WechatAccount.create_v3_account(
                    account_id=str(config.id),
                    account_name=config.name,
                    wxid=config.wxid or f"auto_{config.id}",
                    base_url=config.wechat_base_url,
                    gewe_token=config.token,
                    gewe_appid=config.app_id or "",
                    priority=config.id
                )
            else:
                logger.warning(f"不支持的API版本: {config.api_version}")
                return None

            # 设置状态和扩展配置
            account.status = status
            account.extra_config = {
                'agent_id': config.id,
                'device_id': config.device_id,
                'last_check_time': config.last_check_time
            }

            return account

        except Exception as e:
            logger.error(f"创建账号失败 {config.name}: {e}")
            return None

    def _get_available_member(self, member_index: Optional[int] = None) -> Optional[MemberAdapter]:
        """
        获取可用成员

        Args:
            member_index: 指定成员索引，None表示自动选择

        Returns:
            可用的成员适配器
        """
        if not self.member_adapters:
            return None

        # 如果指定了成员索引
        if member_index is not None:
            if 0 <= member_index < len(self.member_adapters):
                member = self.member_adapters[member_index]
                if member.is_available():
                    return member
            return None

        # 自动选择：优先使用ID最靠前的可用成员
        for member in self.member_adapters:
            if member.is_available() and member.can_call_now():
                return member

        # 如果没有可用成员（考虑频率限制），忽略频率限制重新选择
        for member in self.member_adapters:
            if member.is_available():
                logger.warning(f"忽略频率限制，使用成员: {member.account.account_name}")
                return member

        return None

    def _call_api_with_member(self, method_name: str, member_index: Optional[int] = None, *args, **kwargs) -> APIResponse:
        """
        使用指定或自动选择的成员调用API

        Args:
            method_name: API方法名
            member_index: 指定成员索引，None表示自动选择
            *args, **kwargs: API参数

        Returns:
            API响应
        """
        member = self._get_available_member(member_index)

        if not member:
            return APIResponse(ok=False, message="没有可用的成员")

        try:
            # 标记调用
            member.mark_call()

            # 执行API调用
            method = getattr(member.adapter, method_name)
            response = method(*args, **kwargs)

            # 更新统计
            if response.ok:
                member.account.mark_success()
                self.stats['messages_sent'] += 1
                logger.info(f"API调用成功，使用成员: {member.account.account_name}")
            else:
                member.account.mark_error()
                self.stats['errors'] += 1
                logger.warning(f"API调用失败，成员: {member.account.account_name}, 错误: {response.message}")

            return response

        except Exception as e:
            member.account.mark_error()
            self.stats['errors'] += 1
            logger.error(f"API调用异常，成员: {member.account.account_name}, 异常: {e}")
            return APIResponse(ok=False, message=str(e))

    def send_text(self, request: SendTextRequest, member_index: Optional[int] = None) -> APIResponse:
        """发送文本消息"""
        return self._call_api_with_member('send_text', member_index, request)

    def send_image(self, request: SendImageRequest, member_index: Optional[int] = None) -> APIResponse:
        """发送图片消息"""
        return self._call_api_with_member('send_image', member_index, request)

    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，开始关闭...")
            self.shutdown()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    async def _check_member_health(self):
        """检查成员在线状态"""
        if not self.member_adapters:
            return

        for member in self.member_adapters:
            try:
                # 调用在线状态检查API
                request = GetOnlineStatusRequest(wxid=member.account.wxid)
                response = member.adapter.get_online_status(request)

                # 更新在线状态
                if response.ok:
                    member.is_online = True
                    member.account.mark_success()
                    # 更新数据库中的登录状态为在线
                    self._update_member_login_status(member, True)
                    logger.debug(f"成员 {member.account.account_name} 在线状态检查成功")
                else:
                    member.is_online = False
                    member.account.mark_error()
                    # 更新数据库中的登录状态为离线
                    self._update_member_login_status(member, False)
                    logger.warning(f"成员 {member.account.account_name} 在线状态检查失败: {response.message}")

                member.last_health_check = time.time()

            except Exception as e:
                member.is_online = False
                member.account.mark_error()
                # 更新数据库中的登录状态为离线
                self._update_member_login_status(member, False)
                logger.error(f"成员 {member.account.account_name} 健康检查异常: {e}")

        self.stats['health_checks'] += 1

    def _update_member_login_status(self, member: MemberAdapter, is_online: bool):
        """更新成员在数据库中的登录状态"""
        try:
            agent_id = member.account.extra_config.get('agent_id')
            if agent_id:
                login_status = 1 if is_online else 0
                success = self.db_manager.update_login_status(agent_id, login_status)
                if success:
                    logger.debug(f"更新成员 {member.account.account_name} 数据库登录状态: {'在线' if is_online else '离线'}")
                else:
                    logger.warning(f"更新成员 {member.account.account_name} 数据库登录状态失败")
            else:
                logger.warning(f"成员 {member.account.account_name} 缺少 agent_id，无法更新数据库状态")
        except Exception as e:
            logger.error(f"更新成员 {member.account.account_name} 数据库登录状态异常: {e}")

    async def _health_check_loop(self):
        """健康检查循环"""
        while not self.shutdown_event.is_set():
            try:
                await self._check_member_health()

                # 输出健康状态
                online_count = sum(1 for m in self.member_adapters if m.is_online)
                available_count = sum(1 for m in self.member_adapters if m.is_available())
                logger.info(f"健康检查完成 - 在线: {online_count}/{len(self.member_adapters)}, 可用: {available_count}/{len(self.member_adapters)}")

                # 等待下次检查（每分钟），但每秒检查一次关闭事件
                for _ in range(60):
                    if self.shutdown_event.is_set():
                        break
                    await asyncio.sleep(1)

            except asyncio.CancelledError:
                logger.info("健康检查循环被取消")
                break
            except Exception as e:
                logger.error(f"健康检查异常: {e}")
                # 等待10秒，但每秒检查一次关闭事件
                for _ in range(10):
                    if self.shutdown_event.is_set():
                        break
                    await asyncio.sleep(1)

    async def _consume_product_matches(self):
        """消费商品匹配数据"""
        if not PIKA_AVAILABLE:
            logger.warning("aio_pika不可用，跳过商品匹配消费")
            return

        try:
            conn = await aio_pika.connect_robust(self.mq_url)
            ch = await conn.channel()

            # 声明交换机和队列
            ex = await ch.declare_exchange("events.product-matches", aio_pika.ExchangeType.TOPIC, durable=True)
            queue_name = f"{self.group_name}.product-matches"

            args = {
                "x-message-ttl": 300000,  # 5分钟TTL
                "x-max-length": 5000,    # 最多5千条消息
                "x-overflow": "drop-head" # 超限丢弃最老消息
            }

            q = await ch.declare_queue(queue_name, durable=True, arguments=args)
            await q.bind(ex, routing_key="product.matches.store.*")

            logger.info(f"开始消费商品匹配数据: {queue_name}")

            async with q.iterator() as it:
                async for message in it:
                    if self.shutdown_event.is_set():
                        break

                    async with message.process():
                        try:
                            event = json.loads(message.body.decode("utf-8"))
                            await self._process_product_match(event)

                        except Exception as e:
                            logger.error(f"处理商品匹配消息异常: {e}")

        except Exception as e:
            logger.error(f"商品匹配消费异常: {e}")

    async def _process_product_match(self, event: Dict[str, Any]):
        """处理商品匹配事件"""
        try:
            store = event.get("store", {})
            products = event.get("products", [])

            store_name = store.get("name", "未知店铺")
            product_count = len(products)

            # 构造消息内容
            content = f"V2匹配结果:\n店铺: {store_name}\n匹配商品数: {product_count}"

            if products:
                content += "\n\n"
                for i, product in enumerate(products):
                    product_name = product.get("name", "未知商品")
                    content += f"\n{i+1}. {product_name}"

            # 发送到测试群
            request = SendTextRequest(
                wxid="auto",  # 自动选择成员
                to_wxid=self.test_group_id,
                content=content
            )

            response = self.send_text(request)

            if response.ok:
                logger.info(f"商品匹配通知发送成功: {store_name}, {product_count}个商品")
            else:
                logger.error(f"商品匹配通知发送失败: {response.message}")

            self.stats['messages_processed'] += 1

        except Exception as e:
            logger.error(f"处理商品匹配事件异常: {e}")

    async def _consume_wxid_messages(self):
        """消费所有成员wxid的MQ消息"""
        if not PIKA_AVAILABLE:
            logger.warning("aio_pika不可用，跳过wxid消息消费")
            return

        if not self.enable_wxid_consumer:
            logger.info("wxid消息消费功能已禁用，跳过")
            return

        successful_consumers = 0
        total_members = len(self.member_adapters)

        try:
            # 设置连接超时
            conn = await aio_pika.connect_robust(
                self.mq_url,
                timeout=10,  # 连接超时10秒
                heartbeat=600  # 心跳间隔10分钟
            )
            ch = await conn.channel()

            # 设置通道QoS和超时
            await ch.set_qos(prefetch_count=1)

            # 预定义队列参数（与接收适配器保持一致）
            queue_args = {
                "x-message-ttl": 60000,      # 消息TTL 1分钟
                "x-expires": 3600000,        # 队列TTL 1小时
                "x-max-length": 3000,        # 队列最大长度
                "x-overflow": "drop-head",   # 超限时丢弃最老消息
            }

            logger.info(f"开始为 {total_members} 个成员创建wxid消息消费者...")
            logger.info("主动创建队列以确保及时处理未处理消息")

            # 为每个成员创建消费者
            for member in self.member_adapters:
                wxid = member.account.wxid
                queue_name = f"unhandled.{wxid}"

                try:
                    # 添加超时控制
                    queue_task = asyncio.wait_for(
                        self._setup_wxid_queue(ch, queue_name, queue_args, wxid),
                        timeout=5.0  # 5秒超时
                    )

                    q = await queue_task
                    if q:
                        successful_consumers += 1
                        logger.info(f"✅ 成功启动 {wxid} 的消息消费者")

                except asyncio.TimeoutError:
                    logger.warning(f"⏰ 设置 {wxid} 的消息队列超时（5秒）")
                except Exception as e:
                    error_msg = str(e)
                    if "timeout" in error_msg.lower() or "Channel closed" in error_msg:
                        logger.warning(f"⏰ 连接 {queue_name} 超时: {error_msg}")
                    else:
                        logger.warning(f"❌ 设置 {wxid} 的消息队列失败: {error_msg}")



            # 报告结果
            if successful_consumers > 0:
                logger.info(f"🎯 wxid消息消费者启动完成: {successful_consumers}/{total_members} 成功")
            else:
                logger.warning(f"⚠️ 没有成功启动任何wxid消息消费者")

            # 保持连接
            while not self.shutdown_event.is_set():
                await asyncio.sleep(1)

        except asyncio.CancelledError:
            logger.info("wxid消息消费被取消")
        except Exception as e:
            logger.error(f"wxid消息消费异常: {e}")
        finally:
            try:
                if 'conn' in locals():
                    await conn.close()
            except Exception:
                pass

    async def _setup_wxid_queue(self, channel, queue_name: str, queue_args: dict, wxid: str):
        """设置wxid队列并创建消费者"""
        try:
            # 尝试声明队列（主动创建）
            try:
                q = await channel.declare_queue(
                    queue_name,
                    durable=True,
                    arguments=queue_args
                )
                message_count = q.declaration_result.message_count
                logger.debug(f"队列 {queue_name} 已创建/存在，消息数: {message_count}")

            except Exception as declare_error:
                # 如果创建失败，尝试被动连接（队列可能已存在但参数不同）
                if "PRECONDITION_FAILED" in str(declare_error):
                    logger.debug(f"队列 {queue_name} 已存在但参数不同，使用被动模式")
                    q = await channel.declare_queue(queue_name, passive=True)
                else:
                    raise declare_error

            # 创建消息处理器
            async def message_handler(message: aio_pika.IncomingMessage):
                async with message.process():
                    try:
                        body = message.body.decode('utf-8')
                        data = json.loads(body)

                        # 打印到控制台
                        logger.info(f"📨 收到 {wxid} 的未处理消息:")
                        logger.info(f"   消息ID: {data.get('msg_id', 'unknown')}")

                        # 正确获取发送者信息
                        from_user = data.get('from_user_name', data.get('from_name', 'unknown'))
                        logger.info(f"   发送者: {from_user}")

                        # 获取群组信息
                        to_user = data.get('to_user_name', 'unknown')
                        group_id = data.get('group_id')
                        if group_id:
                            logger.info(f"   来源群: {group_id}")
                        elif to_user != wxid and '@chatroom' in to_user:
                            logger.info(f"   来源群: {to_user}")
                        else:
                            logger.info(f"   接收者: {to_user}")

                        logger.info(f"   内容: {data.get('content', 'unknown')}")

                        # 格式化时间戳
                        timestamp = data.get('timestamp', 0)
                        if isinstance(timestamp, (int, float)) and timestamp > 0:
                            import datetime
                            formatted_time = datetime.datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                            logger.info(f"   时间: {formatted_time} ({timestamp})")
                        else:
                            logger.info(f"   时间: {timestamp}")

                        # 这里可以添加更多的消息处理逻辑
                        await self._process_unhandled_message(data, wxid)

                    except json.JSONDecodeError as e:
                        logger.warning(f"wxid {wxid} 消息JSON解析失败: {e}")
                        logger.debug(f"原始消息: {body[:200]}...")
                    except Exception as e:
                        logger.error(f"处理wxid {wxid} 消息异常: {e}")

            # 开始消费
            await q.consume(message_handler)
            return q

        except Exception as e:
            logger.error(f"设置队列 {queue_name} 失败: {e}")
            return None

    async def _process_unhandled_message(self, message_data: dict, wxid: str):
        """处理未处理的消息 - 可以在这里添加业务逻辑"""
        try:
            msg_type = message_data.get('msg_type', 0)
            content = message_data.get('content', '')
            msg_id = message_data.get('msg_id', 'unknown')
            from_user = message_data.get('from_user_name', message_data.get('from_name', 'unknown'))
            to_user = message_data.get('to_user_name', 'unknown')
            timestamp = message_data.get('timestamp', 0)

            # 判断是否是群消息
            is_group_message = '@chatroom' in to_user or message_data.get('group_id') is not None

            # 根据消息类型进行不同处理
            if msg_type == 1:  # 文本消息
                logger.debug(f"处理文本消息 {msg_id}: 发送者={from_user}, 内容={content[:50]}...")

                # 可以在这里添加文本消息的特殊处理逻辑
                # 例如：关键词检测、自动回复等
                await self._handle_text_message(message_data, wxid, from_user, content, is_group_message)

            elif msg_type == 3:  # 图片消息
                logger.debug(f"处理图片消息 {msg_id}: 发送者={from_user}")
                await self._handle_image_message(message_data, wxid, from_user, is_group_message)

            elif msg_type == 47:  # 表情消息
                logger.debug(f"处理表情消息 {msg_id}: 发送者={from_user}")
                await self._handle_emoji_message(message_data, wxid, from_user, is_group_message)

            else:
                logger.debug(f"处理消息类型 {msg_type}, ID: {msg_id}, 发送者={from_user}")
                await self._handle_other_message(message_data, wxid, from_user, is_group_message)

            # 更新统计
            self.stats['messages_processed'] += 1

        except Exception as e:
            logger.error(f"处理未处理消息异常: {e}")

    async def _handle_text_message(self, message_data: dict, wxid: str, from_user: str, content: str, is_group: bool):
        """处理文本消息的业务逻辑"""
        try:
            # 这里可以添加文本消息的具体处理逻辑
            # 例如：
            # 1. 关键词检测和自动回复
            # 2. 转发到其他系统
            # 3. 存储到数据库
            # 4. 触发特定业务流程

            # 示例：简单的关键词检测
            keywords = ['帮助', 'help', '客服']
            if any(keyword in content.lower() for keyword in keywords):
                logger.info(f"检测到帮助请求: {from_user} 在 {'群聊' if is_group else '私聊'} 中说: {content}")
                # 这里可以触发自动回复逻辑

        except Exception as e:
            logger.error(f"处理文本消息业务逻辑异常: {e}")

    async def _handle_image_message(self, message_data: dict, wxid: str, from_user: str, is_group: bool):
        """处理图片消息的业务逻辑"""
        try:
            # 这里可以添加图片消息的处理逻辑
            # 例如：图片识别、存储等
            logger.debug(f"图片消息处理: {from_user} 发送了图片")
        except Exception as e:
            logger.error(f"处理图片消息业务逻辑异常: {e}")

    async def _handle_emoji_message(self, message_data: dict, wxid: str, from_user: str, is_group: bool):
        """处理表情消息的业务逻辑"""
        try:
            # 这里可以添加表情消息的处理逻辑
            logger.debug(f"表情消息处理: {from_user} 发送了表情")
        except Exception as e:
            logger.error(f"处理表情消息业务逻辑异常: {e}")

    async def _handle_other_message(self, message_data: dict, wxid: str, from_user: str, is_group: bool):
        """处理其他类型消息的业务逻辑"""
        try:
            # 这里可以添加其他消息类型的处理逻辑
            msg_type = message_data.get('msg_type', 0)
            logger.debug(f"其他消息处理: {from_user} 发送了类型 {msg_type} 的消息")
        except Exception as e:
            logger.error(f"处理其他消息业务逻辑异常: {e}")

    def _handle_message(self, group_name: str, message: Dict[str, Any]):
        """
        处理接收到的消息（保留原有接口兼容性）

        Args:
            group_name: 组名
            message: 消息内容
        """
        try:
            self.stats['messages_processed'] += 1

            message_type = message.get('type')
            logger.info(f"处理组 {group_name} 的消息: {message_type}")

            response = None
            member_index = message.get('member_index')  # 可选的成员索引

            if message_type == 'send_text':
                # 发送文本消息
                request = SendTextRequest(
                    wxid=message.get('wxid', 'auto'),
                    to_wxid=message.get('to_wxid'),
                    content=message.get('content'),
                    at=message.get('at', [])
                )
                response = self.send_text(request, member_index)

            elif message_type == 'send_image':
                # 发送图片消息
                request = SendImageRequest(
                    wxid=message.get('wxid', 'auto'),
                    to_wxid=message.get('to_wxid'),
                    image_base64=message.get('image_url')
                )
                response = self.send_image(request, member_index)

            elif message_type == 'health_check':
                # 健康检查
                stats = self.get_status()
                response = APIResponse(ok=True, data=stats)

            else:
                logger.warning(f"未知的消息类型: {message_type}")
                response = APIResponse(ok=False, message=f"未知的消息类型: {message_type}")

            # 更新统计
            if response and response.ok:
                logger.info(f"消息处理成功: {message.get('message_id', 'unknown')}")
            else:
                logger.error(f"消息处理失败: {response.message if response else 'unknown error'}")

        except Exception as e:
            self.stats['errors'] += 1
            logger.error(f"处理消息异常: {e}")

    async def _stats_loop(self):
        """统计信息输出循环"""
        while not self.shutdown_event.is_set():
            try:
                # 输出统计信息
                uptime = time.time() - self.stats['start_time']
                available_count = sum(1 for m in self.member_adapters if m.is_available())
                online_count = sum(1 for m in self.member_adapters if m.is_online)

                logger.info(
                    f"运行统计 - 运行时间: {uptime:.0f}s, "
                    f"处理消息: {self.stats['messages_processed']}, "
                    f"发送成功: {self.stats['messages_sent']}, "
                    f"错误: {self.stats['errors']}, "
                    f"健康检查: {self.stats['health_checks']}"
                )

                logger.info(
                    f"成员状态 - 总数: {len(self.member_adapters)}, "
                    f"在线: {online_count}, "
                    f"可用: {available_count}"
                )

                # 等待下次输出（每5分钟），但每秒检查一次关闭事件
                for _ in range(300):
                    if self.shutdown_event.is_set():
                        break
                    await asyncio.sleep(1)

            except asyncio.CancelledError:
                logger.info("统计循环被取消")
                break
            except Exception as e:
                logger.error(f"统计输出异常: {e}")
                # 等待60秒，但每秒检查一次关闭事件
                for _ in range(60):
                    if self.shutdown_event.is_set():
                        break
                    await asyncio.sleep(1)

    def start(self):
        """启动应用程序"""
        if self.is_running:
            logger.warning("应用程序已在运行")
            return

        if not self.member_adapters:
            logger.error("没有可用的成员，无法启动")
            return

        logger.info(f"启动微信API应用程序 - 组: {self.group_name}")

        try:
            # 设置信号处理器
            self._setup_signal_handlers()

            self.is_running = True

            # 创建事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            # 创建主任务
            async def main_task():
                # 启动异步任务
                tasks = [
                    asyncio.create_task(self._health_check_loop()),
                    asyncio.create_task(self._stats_loop()),
                    asyncio.create_task(self._consume_product_matches())
                ]

                # 可选的wxid消息消费任务
                if self.enable_wxid_consumer:
                    tasks.append(asyncio.create_task(self._consume_wxid_messages()))
                    logger.info("启用wxid消息消费功能")
                else:
                    logger.info("禁用wxid消息消费功能")

                logger.info("微信API应用程序启动成功")

                try:
                    # 等待任务完成或关闭事件
                    await asyncio.gather(*tasks, return_exceptions=True)
                except Exception as e:
                    logger.error(f"任务执行异常: {e}")
                finally:
                    # 取消所有任务
                    for task in tasks:
                        if not task.done():
                            task.cancel()

                    # 等待任务取消完成
                    if tasks:
                        try:
                            await asyncio.gather(*tasks, return_exceptions=True)
                        except Exception:
                            pass  # 忽略取消异常

            # 运行事件循环
            try:
                loop.run_until_complete(main_task())
            except KeyboardInterrupt:
                logger.info("收到中断信号")
            finally:
                # 强制关闭事件循环
                try:
                    # 取消所有剩余任务
                    pending = asyncio.all_tasks(loop)
                    for task in pending:
                        task.cancel()

                    # 等待任务取消（最多等待2秒）
                    if pending:
                        loop.run_until_complete(
                            asyncio.wait_for(
                                asyncio.gather(*pending, return_exceptions=True),
                                timeout=2.0
                            )
                        )
                except Exception:
                    pass  # 忽略关闭异常
                finally:
                    loop.close()

        except Exception as e:
            logger.error(f"应用程序启动失败: {e}")
            self.shutdown()

    def shutdown(self):
        """关闭应用程序"""
        if not self.is_running:
            return

        logger.info("正在关闭微信API应用程序...")

        # 设置关闭标志
        self.shutdown_event.set()
        self.is_running = False

        try:
            # 关闭数据库连接
            self.db_manager.close()

            logger.info("微信API应用程序已关闭")
            exit()

        except Exception as e:
            logger.error(f"关闭应用程序时出错: {e}")

    def get_status(self) -> Dict[str, Any]:
        """获取应用程序状态"""
        uptime = time.time() - self.stats['start_time']
        available_count = sum(1 for m in self.member_adapters if m.is_available())
        online_count = sum(1 for m in self.member_adapters if m.is_online)

        member_status = []
        for i, member in enumerate(self.member_adapters):
            member_status.append({
                'index': i,
                'id': member.account.account_id,
                'name': member.account.account_name,
                'wxid': member.account.wxid,
                'api_version': member.account.api_version,
                'is_online': member.is_online,
                'is_available': member.is_available(),
                'success_rate': member.account.get_success_rate(),
                'last_call_time': member.last_call_time,
                'last_health_check': member.last_health_check
            })

        return {
            'is_running': self.is_running,
            'group_name': self.group_name,
            'uptime': uptime,
            'stats': self.stats.copy(),
            'member_count': len(self.member_adapters),
            'online_count': online_count,
            'available_count': available_count,
            'rate_limit_interval': self.rate_limit_interval,
            'members': member_status
        }




def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='微信API应用程序 - 单组多成员管理')
    parser.add_argument('group', type=str, help='组名（必需，如：YBA）')
    parser.add_argument('--mock-db', action='store_true', help='使用模拟数据库')
    parser.add_argument('--rate-limit', type=float, default=1.0,
                       help='API调用频率限制间隔（秒，默认1.0）')
    parser.add_argument('--log-level', type=str, default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    parser.add_argument('--test-group', type=str,
                       help='测试群ID（用于发送商品匹配通知）')

    args = parser.parse_args()

    # 配置日志
    logger.remove()
    logger.add(
        sys.stderr,
        level=args.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )


    # 创建应用程序
    try:
        app = WechatApp(
            group_name=args.group,
            use_mock_db=args.mock_db,
            rate_limit_interval=args.rate_limit
        )

        # 启动应用程序
        app.start()

    except ValueError as e:
        logger.error(f"参数错误: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("收到中断信号")
    except Exception as e:
        logger.error(f"应用程序异常: {e}")
        sys.exit(1)
    finally:
        if 'app' in locals():
            app.shutdown()


if __name__ == "__main__":
    main()
