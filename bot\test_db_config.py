#!/usr/bin/env python3
"""
测试数据库配置
"""
import os
import sys
from loguru import logger

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 加载 .env 文件
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ 成功加载 .env 文件")
except ImportError:
    print("❌ python-dotenv 不可用")

from database import DatabaseManager
from app import WechatApp


def test_database_config():
    """测试数据库配置"""
    print("\n🔍 测试数据库配置...")
    
    # 检查环境变量
    use_mock_db_env = os.getenv('USE_MOCK_DB', 'false')
    print(f"环境变量 USE_MOCK_DB: {use_mock_db_env}")
    
    # 测试数据库管理器
    print("\n📊 测试数据库管理器:")
    try:
        # 测试默认配置（应该从环境变量读取）
        db_manager = DatabaseManager()
        print(f"DatabaseManager use_mock: {db_manager.use_mock}")
        
        if not db_manager.use_mock:
            print("✅ 使用真实数据库")
            # 测试查询
            groups = db_manager.get_all_groups()
            print(f"找到组: {groups}")
            
            if "YBA" in groups:
                agents = db_manager.get_agents_by_group("YBA")
                print(f"YBA组成员数: {len(agents)}")
                for agent in agents[:3]:
                    print(f"  - {agent.name} (ID: {agent.id}, API: {agent.api_version})")
        else:
            print("⚠️ 使用模拟数据库")
            
        db_manager.close()
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")


def test_app_config():
    """测试应用程序配置"""
    print("\n🚀 测试应用程序配置...")
    
    try:
        # 测试默认配置（应该从环境变量读取）
        app = WechatApp(group_name="YBA")
        print(f"WechatApp use_mock_db: {app.db_manager.use_mock}")
        
        status = app.get_status()
        print(f"成员总数: {status['member_count']}")
        print(f"可用成员: {status['available_count']}")
        
        if status['members']:
            print("成员列表:")
            for member in status['members'][:3]:
                print(f"  - {member['name']} (API: {member['api_version']})")
        
        app.shutdown()
        print("✅ 应用程序配置测试成功")
        
    except Exception as e:
        print(f"❌ 应用程序配置测试失败: {e}")


def main():
    """主函数"""
    # 配置日志
    logger.remove()
    logger.add(
        sys.stderr,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>"
    )
    
    print("🧪 数据库配置测试")
    print("=" * 50)
    
    # 显示环境变量
    print("📋 环境变量:")
    print(f"DB_HOST: {os.getenv('DB_HOST', 'NOT_SET')}")
    print(f"DB_PORT: {os.getenv('DB_PORT', 'NOT_SET')}")
    print(f"DB_USER: {os.getenv('DB_USER', 'NOT_SET')}")
    print(f"DB_PASSWORD: {'***' if os.getenv('DB_PASSWORD') else 'NOT_SET'}")
    print(f"DB_NAME: {os.getenv('DB_NAME', 'NOT_SET')}")
    print(f"USE_MOCK_DB: {os.getenv('USE_MOCK_DB', 'NOT_SET')}")
    
    # 测试数据库配置
    test_database_config()
    
    # 测试应用程序配置
    test_app_config()
    
    print("\n" + "=" * 50)
    print("✅ 测试完成")


if __name__ == "__main__":
    main()
